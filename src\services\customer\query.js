import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { getBusinessCategory, getAllSpecifications, getSpecificationById } from "./api";

export function useGetBusinessCategory() {
  return useSuspenseQuery({
    queryKey: ["getBusinessCategory"],
    queryFn: () => getBusinessCategory(),
  });
}

export function useGetAllSpecificationsQuery() {
  return useQuery({
    queryKey: ["specifications"],
    queryFn: getAllSpecifications,
  });
}

export function useGetSpecificationByIdQuery(id) {
  return useQuery({
    queryKey: ["specification", id],
    queryFn: () => getSpecificationById(id),
    enabled: !!id,
  });
}
