import { useMutation, useQueryClient } from "@tanstack/react-query";
import { postSpecifications, updateSpecification, deleteSpecification, verifyCustomerKyc } from "./api";

export function usePostSpecificationMutation() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data) => {
            console.log('Mutation function called with data:', data);
            return postSpecifications(data);
        },
        onSuccess: (response) => {
            console.log("Specification created successfully:", response);
            queryClient.invalidateQueries(["specifications"]);
        },
        onError: (error) => {
            console.error("Create failed:", error);
        },
    })
}

export function useUpdateSpecificationMutation() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: updateSpecification,
        onSuccess: (response, { id }) => {
            console.log("Specification updated.");
            queryClient.invalidateQueries(["specification", id]);
            queryClient.invalidateQueries(["specifications"]);
        },
        onError: (error) => {
            console.error("Update failed:", error);
        },
    });
}

export function useDeleteSpecificationMutation() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: deleteSpecification,
        onSuccess: (_, id) => {
            console.log("Specification deleted.");
            queryClient.invalidateQueries(["specifications"]);
            queryClient.invalidateQueries(["specification", id]);
        },
        onError: (error) => {
            console.error("Delete failed:", error);
        },
    });
}

export function useVerifyCustomerKycMutation() {
    return useMutation({
        mutationFn: (data) => verifyCustomerKyc(data),
        onSuccess: (data) => {
            console.log("✅ Customer KYC verification successful:", data);
        },
        onError: (error) => {
            console.error("❌ Customer KYC verification failed:", error.message);
        },
    });
}