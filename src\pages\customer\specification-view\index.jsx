import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Printer, Download } from "lucide-react";
import SpecTemplate1Modified from "../create-specification/_components/templates/SpecTemplate1Modified";
import SpecTemplate2Modified from "../create-specification/_components/templates/SpecTemplate2Modified";
import SpecTemplate3Modified from "../create-specification/_components/templates/SpecTemplate3Modified";

const templates = [
  { id: 1, name: "Simple", component: SpecTemplate1Modified },
  { id: 2, name: "Professional", component: SpecTemplate2Modified },
  { id: 3, name: "Modern", component: SpecTemplate3Modified },
];

// Mock function to fetch a single specification
const fetchSpecification = async (id) => {
  // This would be replaced with an actual API call in a real application
  const mockSpecs = [
    {
      id: 1,
      category: "Electronics",
      subCategory: "Mobile",
      createdAt: "2025-05-23",
      template_id: 3,
      specificationProductData: [
        {
          itemName: "Smartphone",
          quantity: 10,
          unit: "Pieces",
          attributes: "6GB RAM, 128GB Storage, 5G",
          other: "Black color, 1 year warranty",
        },
        {
          itemName: "Charger",
          quantity: 10,
          unit: "Pieces",
          attributes: "Fast charging, USB-C",
          other: "Compatible with all devices",
        },
      ],
    },
    // Other mock specifications...
  ];

  return mockSpecs.find((spec) => spec.id === parseInt(id));
};

export default function SpecificationView() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [specification, setSpecification] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSpecification = async () => {
      setLoading(true);
      try {
        const data = await fetchSpecification(id);
        console.log("Fetched specification:", data);
        setSpecification(data);
      } catch (error) {
        console.error("Error loading specification:", error);
      } finally {
        setLoading(false);
      }
    };

    loadSpecification();
  }, [id]);

  const handlePrint = () => {
    window.print();
  };

  // Find the template component based on the template_id
  const selectedTemplate = templates.find(
    (template) => template.id === specification?.template_id
  );
  const TemplateComponent = selectedTemplate?.component;

  console.log("Current specification:", specification);
  console.log("Template ID:", specification?.template_id);
  console.log("Selected template:", selectedTemplate);
  console.log("Template component:", TemplateComponent);

  if (loading) {
    return <div className="p-6 text-center">Loading specification...</div>;
  }

  if (!specification) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-semibold mb-4">Specification not found</h2>
        <Button onClick={() => navigate("/customer/1/view-all-specification")}>
          Back to Specifications
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate("/customer/1/view-all-specification")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-semibold">
            Specification #{specification.id}
          </h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6 shadow-md">
        <div className="mb-6">
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-500">Category</p>
              <p className="font-medium">{specification.category}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Sub Category</p>
              <p className="font-medium">{specification.subCategory}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Date Created</p>
              <p className="font-medium">
                {new Date(specification.createdAt).toLocaleDateString()}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Template</p>
              <p className="font-medium">
                {selectedTemplate?.name ||
                  `Template ${specification.template_id}`}
              </p>
            </div>
          </div>
        </div>

        <div className="print:mt-8">
          {TemplateComponent ? (
            <TemplateComponent data={specification} />
          ) : (
            <div className="text-center text-red-500">
              Template not found for ID: {specification.template_id}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
