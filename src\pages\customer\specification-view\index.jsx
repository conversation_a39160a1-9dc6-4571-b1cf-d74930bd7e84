import React from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLef<PERSON>, Printer, Download } from "lucide-react";
import SpecTemplate1Modified from "../create-specification/_components/templates/SpecTemplate1Modified";
import SpecTemplate2Modified from "../create-specification/_components/templates/SpecTemplate2Modified";
import SpecTemplate3Modified from "../create-specification/_components/templates/SpecTemplate3Modified";
import { useGetSpecificationByIdQuery } from "@/services/customer/query";
import { useAppSelector } from "@/hooks/StoreHooks";

const templates = [
  { id: 1, name: "Simple", component: SpecTemplate1Modified },
  { id: 2, name: "Professional", component: SpecTemplate2Modified },
  { id: 3, name: "Modern", component: SpecTemplate3Modified },
];

// Function to transform API data to template format
const transformSpecificationForTemplate = (apiData) => {
  if (!apiData) return null;

  return {
    id: apiData.id,
    title: apiData.title,
    description: apiData.description,
    type: apiData.type,
    specificationType: apiData.type, // Template expects this field name
    category: apiData.category || "Uncategorized",
    subCategory: apiData.sub_category || "General",
    createdAt: apiData.created_at,
    template_id: apiData.template_id,
    status: apiData.status,
    // Transform items to match template expected format
    specificationProductData: apiData.items?.map(item => ({
      itemName: item.item_name,
      quantity: parseFloat(item.quantity),
      unit: item.unit,
      attributes: item.specifications,
      other: item.other
    })) || []
  };
};

export default function SpecificationView() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);

  // Use React Query to fetch specification data
  const { data: queryData, isLoading, error } = useGetSpecificationByIdQuery(id);

  // Transform API data for template consumption
  const specification = React.useMemo(() => {
    if (queryData?.success && queryData?.data) {
      return transformSpecificationForTemplate(queryData.data);
    }
    return null;
  }, [queryData]);

  const handlePrint = () => {
    window.print();
  };

  // Find the template component based on the template_id
  const selectedTemplate = templates.find(
    (template) => template.id === specification?.template_id
  );
  const TemplateComponent = selectedTemplate?.component;

  console.log("Current specification:", specification);
  console.log("Template ID:", specification?.template_id);
  console.log("Selected template:", selectedTemplate);
  console.log("Template component:", TemplateComponent);

  if (isLoading) {
    return <div className="p-6 text-center">Loading specification...</div>;
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-semibold mb-4 text-red-600">Error Loading Specification</h2>
        <p className="text-gray-600 mb-4">{error?.message || "Failed to load specification"}</p>
        <Button onClick={() => {
          const userId = user?.userId || "1";
          navigate(`/customer/${userId}/view-all-specification`);
        }}>
          Back to Specifications
        </Button>
      </div>
    );
  }

  if (!specification) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-semibold mb-4">Specification not found</h2>
        <Button onClick={() => {
          const userId = user?.userId || "1";
          navigate(`/customer/${userId}/view-all-specification`);
        }}>
          Back to Specifications
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const userId = user?.userId || "1";
              navigate(`/customer/${userId}/view-all-specification`);
            }}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <h1 className="text-2xl font-semibold">
            {specification.title || `Specification #${specification.id}`}
          </h1>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrint}
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="bg-white rounded-lg border p-6 shadow-md">
        <div className="mb-6">
          {/* Specification Header Info */}
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">{specification.title}</h2>
            {specification.description && (
              <p className="text-gray-600 mb-4">{specification.description}</p>
            )}
            <div className="flex gap-4 mb-4">
              <span className={`px-3 py-1 rounded-full text-sm capitalize ${
                specification.status === 'published'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {specification.status}
              </span>
              <span className="px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 capitalize">
                {specification.type}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <p className="text-sm text-gray-500">Category</p>
              <p className="font-medium">{specification.category}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Sub Category</p>
              <p className="font-medium">{specification.subCategory}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Date Created</p>
              <p className="font-medium">
                {new Date(specification.createdAt).toLocaleDateString()}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Template</p>
              <p className="font-medium">
                {selectedTemplate?.name ||
                  `Template ${specification.template_id}`}
              </p>
            </div>
          </div>
        </div>

        <div className="print:mt-8">
          {TemplateComponent ? (
            <TemplateComponent data={specification} />
          ) : (
            <div className="text-center text-red-500">
              Template not found for ID: {specification.template_id}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
