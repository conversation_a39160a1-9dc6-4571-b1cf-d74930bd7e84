import React from "react";
import { useSpecification } from "@/context/SpecificationContext";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { usePostSpecificationMutation } from "@/services/customer/mutation";
import SpecTemplate1Modified from "../_components/templates/SpecTemplate1Modified";
import SpecTemplate2Modified from "../_components/templates/SpecTemplate2Modified";
import SpecTemplate3Modified from "../_components/templates/SpecTemplate3Modified";

const templates = [
  { id: 1, name: "Simple", component: SpecTemplate1Modified },
  { id: 2, name: "Professional", component: SpecTemplate2Modified },
  { id: 3, name: "Modern", component: SpecTemplate3Modified },
];

const ReviewSpecification = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { specificationProductData, selectedTemplate } = useSpecification();
  const postSpecificationMutation = usePostSpecificationMutation();

  const SelectedTemplateComponent = templates.find(
    (t) => t.id === selectedTemplate
  )?.component;
  
  console.log('Specification data:', specificationProductData);

  const handleSubmit = async () => {
    console.log('handleSubmit function called!');
    
    try {
      if (!specificationProductData) {
        toast({
          title: "Error",
          description: "No specification data found. Please go back and create a specification.",
          variant: "destructive",
        });
        return;
      }

      if (!selectedTemplate) {
        toast({
          title: "Error",
          description: "Please select a template before submitting.",
          variant: "destructive",
        });
        return;
      }

      if (!specificationProductData.category || !specificationProductData.subCategory) {
        toast({
          title: "Error",
          description: "Category and subcategory are required. Please go back and select them.",
          variant: "destructive",
        });
        return;
      }

      if (!specificationProductData.specificationType) {
        toast({
          title: "Error",
          description: "Specification type is required. Please go back and select it.",
          variant: "destructive",
        });
        return;
      }

      if (!specificationProductData.specificationProductData || specificationProductData.specificationProductData.length === 0) {
        toast({
          title: "Error",
          description: "At least one item is required. Please go back and add product details.",
          variant: "destructive",
        });
        return;
      }

      // Validate each item has required fields
      const invalidItems = specificationProductData.specificationProductData.filter(
        item => !item.itemName || !item.quantity || !item.unit
      );

      if (invalidItems.length > 0) {
        toast({
          title: "Error",
          description: "All items must have a name, quantity, and unit. Please complete the missing fields.",
          variant: "destructive",
        });
        return;
      }

      // Create a detailed description
      const itemCount = specificationProductData.specificationProductData.length;
      const totalQuantity = specificationProductData.specificationProductData.reduce((sum, item) => sum + (parseInt(item.quantity) || 0), 0);
      
      const description = `Complete ${specificationProductData.specificationType} specification for ${specificationProductData.category} - ${specificationProductData.subCategory}. Includes ${itemCount} different items with a total quantity of ${totalQuantity} units. This specification covers all requirements for ${specificationProductData.specificationType === "product" ? "goods" : "services"} procurement.`;

      // Prepare the data for API submission
      const submissionData = {
        title: `${specificationProductData.category} - ${specificationProductData.subCategory} Specification`,
        description: description,
        type: specificationProductData.specificationType === "product" ? "goods" : "services",
        category_id: parseInt(specificationProductData.categoryId) || 1,
        sub_category_id: specificationProductData.subcategoryId ? parseInt(specificationProductData.subcategoryId) : null,
        template_id: selectedTemplate,
        status: "published",
        items: specificationProductData.specificationProductData.map((item, index) => ({
          item_name: item.itemName,
          quantity: parseInt(item.quantity) || 1,
          unit: item.unit,
          specifications: item.attributes,
          other: item.other
        })),
      };

      console.log('Submitting specification data:', submissionData);

      // Log the mutation call
      console.log('Calling postSpecificationMutation.mutateAsync...');
      
      const result = await postSpecificationMutation.mutateAsync(submissionData);
      
      console.log('API Response:', result);

      toast({
        title: "Success",
        description: "Specification submitted successfully!",
      });
      
      navigate("/customer/1/dashboard");
    } catch (error) {
      console.error("Specification submission error:", error);
      toast({
        title: "Error",
        description: error?.response?.data?.message || "Failed to submit specification. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-2xl font-semibold mb-4">Review Your Specification</h2>

      <div className="border rounded-lg p-4 mb-6">
        {SelectedTemplateComponent ? (
          <SelectedTemplateComponent data={specificationProductData} />
        ) : (
          <p>No template selected</p>
        )}
      </div>

      <div className="flex justify-end gap-4">
        <button
          className="border border-gray-400 text-gray-700 px-4 py-2 rounded"
          onClick={() => navigate("/customer/1/create-specification/preview")}
        >
          Go Back
        </button>
        <button
          className="bg-primary text-black px-6 py-2 rounded font-bold disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={handleSubmit}
          disabled={postSpecificationMutation.isPending}
        >
          {postSpecificationMutation.isPending ? "Submitting..." : "Confirm & Submit"}
        </button>
      </div>
    </div>
  );
};

export default ReviewSpecification;
