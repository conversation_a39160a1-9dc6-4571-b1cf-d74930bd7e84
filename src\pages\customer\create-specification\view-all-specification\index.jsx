import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Filter, X, Eye, RefreshCw } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { useGetAllSpecificationsQuery } from "@/services/customer/query";
import { useAppSelector } from "@/hooks/StoreHooks";

// Function to transform API data to component structure
const transformSpecificationData = (apiData) => {
  if (!apiData || !Array.isArray(apiData)) return [];

  return apiData.map(spec => ({
    id: spec.id,
    title: spec.title,
    description: spec.description,
    type: spec.type,
    category: spec.category || "Uncategorized",
    subCategory: spec.sub_category || "General",
    createdAt: spec.created_at,
    template_id: spec.template_id,
    status: spec.status,
    items: spec.items || [],
    items_count: spec.items_count || 0
  }));
};

export default function ViewAllSpecifications() {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  const { data: queryData, isLoading, error: queryError, refetch } = useGetAllSpecificationsQuery();

  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: "",
    subCategory: "",
    date: "",
  });

  // Transform and get specifications data
  const specs = React.useMemo(() => {
    if (queryData?.success && queryData?.data) {
      return transformSpecificationData(queryData.data);
    }
    return [];
  }, [queryData]);

  // Get unique categories and subcategories for dropdowns
  const categories = [...new Set(specs.map((spec) => spec.category))];
  const subCategories = [...new Set(specs.map((spec) => spec.subCategory))];

  const handleFilterChange = (name, value) => {
    setFilters({ ...filters, [name]: value });
  };

  const resetFilters = () => {
    setFilters({
      category: "",
      subCategory: "",
      date: "",
    });
  };

  const filteredSpecs = specs.filter((spec) => {
    const specDate = format(new Date(spec.createdAt), "yyyy-MM-dd");
    return (
      (filters.category === "all" ||
        !filters.category ||
        spec.category === filters.category) &&
      (filters.subCategory === "all" ||
        !filters.subCategory ||
        spec.subCategory === filters.subCategory) &&
      (filters.date ? specDate === filters.date : true)
    );
  });

  const handleViewSpecification = (id) => {
    const userId = user?.userId || "1"; // Fallback to "1" if user ID not available
    navigate(`/customer/${userId}/specification-view/${id}`);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Specifications</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => refetch()}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <Label className="mb-2 block">Category</Label>
                <Select
                  value={filters.category}
                  onValueChange={(value) =>
                    handleFilterChange("category", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Sub Category</Label>
                <Select
                  value={filters.subCategory}
                  onValueChange={(value) =>
                    handleFilterChange("subCategory", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select sub-category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sub-categories</SelectItem>
                    {subCategories.map((subCategory) => (
                      <SelectItem key={subCategory} value={subCategory}>
                        {subCategory}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Date</Label>
                <Input
                  type="date"
                  value={filters.date}
                  onChange={(e) => handleFilterChange("date", e.target.value)}
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Reset Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="bg-white rounded-md border shadow-sm w-full">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-gray-500">Loading specifications...</div>
          </div>
        ) : queryError ? (
          <div className="flex justify-center items-center py-8">
            <div className="text-red-500">
              {queryError?.message || "Failed to load specifications. Please try again."}
            </div>
          </div>
        ) : (
          <Table className="cursor-pointer w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[20%]">Title</TableHead>
                <TableHead className="w-[15%]">Type</TableHead>
                <TableHead className="w-[15%]">Category</TableHead>
                <TableHead className="w-[15%]">Sub Category</TableHead>
                <TableHead className="w-[10%]">Items</TableHead>
                <TableHead className="w-[10%]">Status</TableHead>
                <TableHead className="w-[10%]">Date</TableHead>
                <TableHead className="w-[5%] text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSpecs.length > 0 ? (
                filteredSpecs.map((spec) => (
                  <TableRow key={spec.id} className="hover:bg-gray-100">
                    <TableCell className="font-medium">
                      <div>
                        <div className="font-semibold">{spec.title}</div>
                        {spec.description && (
                          <div className="text-sm text-gray-500 truncate max-w-xs">
                            {spec.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">{spec.type}</span>
                    </TableCell>
                    <TableCell>{spec.category}</TableCell>
                    <TableCell>{spec.subCategory}</TableCell>
                    <TableCell>
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                        {spec.items_count} items
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs capitalize ${
                        spec.status === 'published'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {spec.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      {format(new Date(spec.createdAt), "yyyy-MM-dd")}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewSpecification(spec.id)}
                        className="flex items-center gap-1 ml-auto"
                      >
                        <Eye className="h-4 w-4" />
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-6 text-gray-500"
                  >
                    No specifications found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}
      </div>
    </div>
  );
}
