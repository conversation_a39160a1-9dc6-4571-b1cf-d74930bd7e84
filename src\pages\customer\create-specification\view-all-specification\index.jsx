import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { format } from "date-fns";
import SpecTemplate1Modified from "../_components/templates/SpecTemplate1Modified";
import SpecTemplate2Modified from "../_components/templates/SpecTemplate2Modified";
import SpecTemplate3Modified from "../_components/templates/SpecTemplate3Modified";
import { Button } from "@/components/ui/button";
import { Filter, X, Eye } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";

const templates = {
  1: SpecTemplate1Modified,
  2: SpecTemplate2Modified,
  3: SpecTemplate3Modified,
};

// Mock data with the correct structure based on the template components
const fetchSpecifications = async () => {
  return [
    {
      id: 1,
      category: "Electronics",
      subCategory: "Mobile",
      createdAt: "2025-05-23",
      template_id: 3,
      specificationProductData: [
        {
          itemName: "Smartphone",
          quantity: 10,
          unit: "Pieces",
          attributes: "6GB RAM, 128GB Storage, 5G",
          other: "Black color, 1 year warranty",
        },
        {
          itemName: "Charger",
          quantity: 10,
          unit: "Pieces",
          attributes: "Fast charging, USB-C",
          other: "Compatible with all devices",
        },
      ],
    },
    {
      id: 2,
      category: "Furniture",
      subCategory: "Chair",
      createdAt: "2025-05-21",
      template_id: 1,
      specificationProductData: [
        {
          itemName: "Office Chair",
          quantity: 5,
          unit: "Pieces",
          attributes: "Ergonomic, Adjustable height",
          other: "Black leather, with armrests",
        },
      ],
    },
    {
      id: 3,
      category: "Electronics",
      subCategory: "Computer",
      createdAt: "2025-05-18",
      template_id: 2,
      specificationProductData: [
        {
          itemName: "Laptop",
          quantity: 3,
          unit: "Pieces",
          attributes: "16GB RAM, 512GB SSD, Intel i7",
          other: "Windows 11, 3-year warranty",
        },
        {
          itemName: "Mouse",
          quantity: 3,
          unit: "Pieces",
          attributes: "Wireless, Ergonomic",
          other: "Rechargeable battery",
        },
        {
          itemName: "Keyboard",
          quantity: 3,
          unit: "Pieces",
          attributes: "Mechanical, RGB backlight",
          other: "Programmable keys",
        },
      ],
    },
    {
      id: 4,
      category: "Office Supplies",
      subCategory: "Stationery",
      createdAt: "2025-05-15",
      template_id: 1,
      specificationProductData: [
        {
          itemName: "Notebooks",
          quantity: 20,
          unit: "Pieces",
          attributes: "A4 size, Hardcover",
          other: "100 pages, ruled",
        },
        {
          itemName: "Pens",
          quantity: 50,
          unit: "Pieces",
          attributes: "Ballpoint, Blue ink",
          other: "0.7mm tip",
        },
      ],
    },
    {
      id: 5,
      category: "Furniture",
      subCategory: "Desk",
      createdAt: "2025-05-10",
      template_id: 3,
      specificationProductData: [
        {
          itemName: "Standing Desk",
          quantity: 2,
          unit: "Pieces",
          attributes: "Electric height adjustment, 160x80cm",
          other: "White finish, cable management included",
        },
      ],
    },
    {
      id: 6,
      category: "Electronics",
      subCategory: "Audio",
      createdAt: "2025-05-05",
      template_id: 2,
      specificationProductData: [
        {
          itemName: "Wireless Headphones",
          quantity: 5,
          unit: "Pieces",
          attributes: "Noise cancelling, Bluetooth 5.0",
          other: "30-hour battery life, Over-ear design",
        },
        {
          itemName: "Portable Speaker",
          quantity: 2,
          unit: "Pieces",
          attributes: "Waterproof, 20W output",
          other: "12-hour battery life",
        },
      ],
    },
  ];
};

export default function ViewAllSpecifications() {
  const navigate = useNavigate();
  const [specs, setSpecs] = useState([]);
  const [selectedSpec, setSelectedSpec] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: "",
    subCategory: "",
    date: "",
  });

  // Get unique categories and subcategories for dropdowns
  const categories = [...new Set(specs.map((spec) => spec.category))];
  const subCategories = [...new Set(specs.map((spec) => spec.subCategory))];

  useEffect(() => {
    const load = async () => {
      const data = await fetchSpecifications();
      setSpecs(data);
    };
    load();
  }, []);

  const handleFilterChange = (name, value) => {
    setFilters({ ...filters, [name]: value });
  };

  const resetFilters = () => {
    setFilters({
      category: "",
      subCategory: "",
      date: "",
    });
  };

  const filteredSpecs = specs.filter((spec) => {
    return (
      (filters.category === "all" ||
        !filters.category ||
        spec.category === filters.category) &&
      (filters.subCategory === "all" ||
        !filters.subCategory ||
        spec.subCategory === filters.subCategory) &&
      (filters.date ? spec.createdAt === filters.date : true)
    );
  });

  const handleViewSpecification = (id) => {
    navigate(`/customer/1/specification-view/${id}`);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Specifications</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            {showFilters ? "Hide Filters" : "Show Filters"}
          </Button>
        </div>
      </div>

      {showFilters && (
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <Label className="mb-2 block">Category</Label>
                <Select
                  value={filters.category}
                  onValueChange={(value) =>
                    handleFilterChange("category", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Sub Category</Label>
                <Select
                  value={filters.subCategory}
                  onValueChange={(value) =>
                    handleFilterChange("subCategory", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select sub-category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sub-categories</SelectItem>
                    {subCategories.map((subCategory) => (
                      <SelectItem key={subCategory} value={subCategory}>
                        {subCategory}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="mb-2 block">Date</Label>
                <Input
                  type="date"
                  value={filters.date}
                  onChange={(e) => handleFilterChange("date", e.target.value)}
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                size="sm"
                onClick={resetFilters}
                className="flex items-center gap-2"
              >
                <X className="h-4 w-4" />
                Reset Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="bg-white rounded-md border shadow-sm w-full">
        <Table className="cursor-pointer w-full">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[25%]">Category</TableHead>
              <TableHead className="w-[25%]">Sub Category</TableHead>
              <TableHead className="w-[25%]">Date</TableHead>
              <TableHead className="w-[15%] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredSpecs.length > 0 ? (
              filteredSpecs.map((spec) => (
                <TableRow key={spec.id} className="hover:bg-gray-100">
                  <TableCell>{spec.category}</TableCell>
                  <TableCell>{spec.subCategory}</TableCell>
                  <TableCell>
                    {format(new Date(spec.createdAt), "yyyy-MM-dd")}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewSpecification(spec.id)}
                      className="flex items-center gap-1 ml-auto"
                    >
                      <Eye className="h-4 w-4" />
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={5}
                  className="text-center py-6 text-gray-500"
                >
                  No specifications found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
