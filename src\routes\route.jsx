import Loader from "@/components/ui/loader";

import { lazy, Suspense } from "react";
import { createBrowser<PERSON>outer, RouterProvider, Navigate } from "react-router-dom";
import ProtectedRoute from "@/routes/protected-route";
import AuthGuard from "@/routes/auth-guard";
import { SpecificationProvider } from "@/context/SpecificationContext";
import KYCForm from "@/pages/auth/kyc-verify/KYCForm";

import AdminLayout from "@/pages/admin/layout";
import AdminDashboardPage from "@/pages/admin/dashboard";
import UserManagementPage from "@/pages/admin/user-management";
import DocumentVerificationPage from "@/pages/admin/document-verification";
import CategoriesPage from "@/pages/admin/category";
import SubcategoriesPage from "@/pages/admin/sub-category";
import ProjectPoolPage from "@/pages/admin/project-pool";
import AllTransactionsPage from "@/pages/admin/project-pool/all-transactions";
import CompareQuotationsPage from "@/pages/admin/project-pool/compare-quotations";
import ImportQuotationsPage from "@/pages/admin/project-pool/import-quotations";
import SupportTrainingResourcesPage from "@/pages/admin/support-and-training/resources";
import SupportTrainingHelpDeskPage from "@/pages/admin/support-and-training/help-desk";
import SupportTrainingTrainPage from "@/pages/admin/support-and-training/training";
import SupportTrainingKnowledgeBasePage from "@/pages/admin/support-and-training/knowledge-base";
import SupportTrainingFeedbackPage from "@/pages/admin/support-and-training/feedback";
import QuotationManagementPage from "@/pages/admin/quotation-management";
import NoticeManagementPage from "@/pages/admin/notice";
import RolesAndPermissionPage from "@/pages/admin/roles-and-permissions";
import ContentManagementPage from "@/pages/admin/content";



// Lazy loading components
// guest imports
const Home = lazy(() => import("@/pages/guest/home/<USER>"));
const About = lazy(() => import("@/pages/guest/about/index"));
const GuestLayout = lazy(() => import("@/pages/guest/layout"));

const LazyNotFoundPage = lazy(() => import("@/routes/not-found"));
const AuthLayout = lazy(() => import("@/pages/auth/layout"));
const WelcomePage = lazy(() => import("@/pages/auth/welcome"));
const ContactPage = lazy(() => import("@/pages/guest/contact/index"));
const TermsOfServicePage = lazy(() =>import("@/pages/guest/terms-of-service/index"));
const PrivacyPolicyPage = lazy(() =>import("@/pages/guest/privacy-policy/index"));


// admin auth
const AdminSignInPage = lazy(() => import("@/pages/auth/admin/signin"));

// auth imports
const VendorVerificationPage = lazy(() =>import("@/pages/auth/vendor-verification"));

// customer imports
// customer auth
const CustomerSignUpPage = lazy(() => import("@/pages/auth/customer/signup"));
const CustomerSignInPage = lazy(() => import("@/pages/auth/customer/signin"));
const CustomerForgotPasswordPage = lazy(() => import("@/pages/auth/customer/forgot-password"));
const CustomerResetPasswordPage = lazy(() => import("@/pages/auth/customer/reset-password"));
import CustomerVerification from "@/pages/auth/customer/kyc-verify/individual-verification/CustomerVerification";

const CustomerLayout = lazy(() => import("@/pages/customer/layout"));
const CustomerDashboardPage = lazy(() => import("@/pages/customer/dashboard"));
const CustomerNoticesPage = lazy(() => import("@/pages/customer/notices"));
const CustomerFeedsPage = lazy(() => import("@/pages/customer/feeds"));
const CreateSpecification = lazy(() =>import("@/pages/customer/create-specification"));
const SpecificationList = lazy(() =>import("@/pages/customer/specification-list"));
const SpecificationReviewPage = lazy(() =>import("@/pages/customer/specification-review"));
const SpecPreview = lazy(() =>import("@/pages/customer/create-specification/preview"));
import ReviewSpecification from "@/pages/customer/create-specification/review/ReviewSpecification";
import ViewAllSpecifications from "@/pages/customer/create-specification/view-all-specification";
import SpecificationView from "@/pages/customer/specification-view";
import SpecificationManagementPage from "@/pages/admin/specification-management";

// vendor imports
const VendorSignUpPage = lazy(() => import("@/pages/auth/vendor/signup"));
const VendorSignInPage = lazy(() => import("@/pages/auth/vendor/signin"));
const VendorForgotPasswordPage = lazy(() => import("@/pages/auth/vendor/forgot-password"));
const VendorResetPasswordPage = lazy(() => import("@/pages/auth/vendor/reset-password"));

const VendorLayout = lazy(() => import("@/pages/vendor/layout"));
const VendorDashboardPage = lazy(() => import("@/pages/vendor/dashboard"));
const FillKycForm = lazy(() => import("@/pages/vendor/fill-kyc-form"));
const VendorFeeds = lazy(() => import("@/pages/vendor/feeds"));
const FeedRequestQuote = lazy(() =>import("@/pages/vendor/feed-request-quote"));
const VendorProfilePage = lazy(() => import("@/pages/vendor/profile/page"));
const CreateQuotation = lazy(() => import("@/pages/vendor/create-quotation"));
const SpecificationsListVendor = lazy(() => import("@/pages/vendor/quotation/SpecificationList"));

const AppRouter = () => {
  const router = createBrowserRouter([
    {
      path: "/",
      element: <GuestLayout />,
      children: [
        {
          path: "",
          element: (
            <Suspense fallback={<Loader />}>
              <Home />
            </Suspense>
          ),
        },
        {
          path: "about",
          element: (
            <Suspense fallback={<Loader />}>
              <About />
            </Suspense>
          ),
        },
        {
          path: "contact",
          element: (
            <Suspense fallback={<Loader />}>
              <ContactPage />
            </Suspense>
          ),
        },
        {
          path: "terms-of-service",
          element: (
            <Suspense fallback={<Loader />}>
              <TermsOfServicePage />
            </Suspense>
          ),
        },
        {
          path: "privacy-policy",
          element: (
            <Suspense fallback={<Loader />}>
              <PrivacyPolicyPage />
            </Suspense>
          ),
        },
      ],
    },
    {
      path: "*",
      element: (
        <Suspense fallback={<Loader />}>
          <LazyNotFoundPage />
        </Suspense>
      ),
    },
    {
      path: "/",
      element: (
        <AuthGuard>
          <AuthLayout />
        </AuthGuard>
      ),
      children: [
        {
          path: "/admin/signin",
          element: (
            <Suspense fallback={<Loader />}>
              <AdminSignInPage />
            </Suspense>
          ),
        },
        {
          path: "/customer/signup",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerSignUpPage />
            </Suspense>
          ),
        },
        {
          path: "/customer/signin",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerSignInPage />
            </Suspense>
          ),
        },
        {
          path: "/customer/forgot-password",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerForgotPasswordPage />
            </Suspense>
          ),
        },
        {
          path: "/customer/reset-password",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerResetPasswordPage />
            </Suspense>
          ),
        },
        {
          path: "/vendor/signup",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorSignUpPage />
            </Suspense>
          ),
        },
        {
          path: "/vendor/signin",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorSignInPage />
            </Suspense>
          ),
        },
        {
          path: "/vendor/forgot-password",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorForgotPasswordPage />
            </Suspense>
          ),
        },
        {
          path: "/vendor/reset-password",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorResetPasswordPage />
            </Suspense>
          ),
        },
        {
          path: "/welcome",
          element: (
            <Suspense fallback={<Loader />}>
              <WelcomePage />
            </Suspense>
          ),
        },
        {
          path: "/verification",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorVerificationPage />
            </Suspense>
          ),
        },
        {
          path: "/vendor/kyc-verify",
          element: (
            <Suspense fallback={<Loader />}>
              <KYCForm />
            </Suspense>
          ),
        },
        {
          path: "/verify",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerVerification />
            </Suspense>
          ),
        },
      ],
    },

    // customer routes
    {
      path: "/customer/:customerId",
      element: (
        <ProtectedRoute routeFor="customer">
          <SpecificationProvider>
            <CustomerLayout />
          </SpecificationProvider>
        </ProtectedRoute>
      ),
      children: [
        {
          path: "",
          element: (
            <Suspense fallback={<Loader />}>
              <LazyNotFoundPage />
            </Suspense>
          ),
        },
        {
          path: "feeds",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerFeedsPage />
            </Suspense>
          ),
        },
        {
          path: "notices",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerNoticesPage />
            </Suspense>
          ),
        },
        {
          path: "dashboard",
          element: (
            <Suspense fallback={<Loader />}>
              <CustomerDashboardPage />
            </Suspense>
          ),
        },
        {
          path: "create-specification",
          element: (
            <Suspense fallback={<Loader />}>
              <CreateSpecification />

            </Suspense>
          ),
        },
        {
          path: "create-specification/preview",
          element: (
            <Suspense fallback={<Loader />}>
              <SpecPreview />
            </Suspense>
          ),
        },
        {
          path: "create-specification/review-specification",
          element: (
            <Suspense fallback={<Loader />}>
              <ReviewSpecification />
            </Suspense>
          ),
        },
        {
          path: "specification-list",
          element: (
            <Suspense fallback={<Loader />}>
              <SpecificationList />
            </Suspense>
          ),
        },
        {
          path: "view-all-specification",
          element: (
            <Suspense fallback={<Loader />}>
              <ViewAllSpecifications />
            </Suspense>
          ),
        },
        {
          path: "specification-view/:id",
          element: (
            <Suspense fallback={<Loader />}>
              <SpecificationView />
            </Suspense>
          ),
        },
        {
          path: "specification/:slug",
          element: (
            <Suspense fallback={<Loader />}>
              <SpecificationReviewPage />
            </Suspense>
          ),
        },
      ],
    },

    // vendor routes
    {
      path: "/vendor/:vendorId",
      element: (
        <ProtectedRoute routeFor="vendor">
          <SpecificationProvider>
            <VendorLayout />
          </SpecificationProvider>
        </ProtectedRoute>
      ),
      children: [
        {
          path: "",
          element: (
            <Suspense fallback={<Loader />}>
              <LazyNotFoundPage />
            </Suspense>
          ),
        },
        {
          path: "fill-kyc-form",
          element: (
            <Suspense fallback={<Loader />}>
              <FillKycForm />
            </Suspense>
          ),
        },
        {
          path: "dashboard",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorDashboardPage />
            </Suspense>
          ),
        },
        {
          path: "feeds",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorFeeds />
            </Suspense>
          ),
        },
        {
          path: "feeds/:feedId/request-quote",
          element: (
            <Suspense fallback={<Loader />}>
              <FeedRequestQuote />
            </Suspense>
          ),
        },
        {
          path: "profile",
          element: (
            <Suspense fallback={<Loader />}>
              <VendorProfilePage />
            </Suspense>
          ),
        },
        {
          path: "create-quotation",
          element: (
            <Suspense fallback={<Loader />}>
              <CreateQuotation />
            </Suspense>
          ),
        },
        {
          path: "request-quotation",
          element: (
            <Suspense fallback={<Loader />}>
              <SpecificationsListVendor />
            </Suspense>
          ),
        },
      ],
    },

    // admin routes
    {
      path: "/admin/:adminId",
      element: (
        <ProtectedRoute routeFor="admin">
          <SpecificationProvider>
            <AdminLayout />
          </SpecificationProvider>
        </ProtectedRoute>
      ),
      children: [
        {
          path: "",
          element: <Navigate to="dashboard" replace />,
        },
        {
          path: "dashboard",
          element: (
            <Suspense fallback={<Loader />}>
              <AdminDashboardPage />
            </Suspense>
          ),
        },
        {
          path: "user-management",
          element: (
            <Suspense fallback={<Loader />}>
              <UserManagementPage />
            </Suspense>
          ),
        },
        {
          path: "document-verification",
          element: (
            <Suspense fallback={<Loader />}>
              <DocumentVerificationPage />
            </Suspense>
          ),
        },
        {
          path: "category",
          element: (
            <Suspense fallback={<Loader />}>
              <CategoriesPage />
            </Suspense>
          ),
        },
        {
          path: "sub-category",
          element: (
            <Suspense fallback={<Loader />}>
              <SubcategoriesPage />
            </Suspense>
          ),
        },
        {
          path: "project-pool",
          element: (
            <Suspense fallback={<Loader />}>
              <ProjectPoolPage />
            </Suspense>
          ),
        },
        {
          path: "project-pool/all-transactions",
          element: (
            <Suspense fallback={<Loader />}>
              <AllTransactionsPage />
            </Suspense>
          ),
        },
        {
          path: "project-pool/compare-quotations",
          element: (
            <Suspense fallback={<Loader />}>
              <CompareQuotationsPage />
            </Suspense>
          ),
        },
        {
          path: "project-pool/import-quotations",
          element: (
            <Suspense fallback={<Loader />}>
              <ImportQuotationsPage />
            </Suspense>
          ),
        },
        {
          path: "support-and-training/resources",
          element: (
            <Suspense fallback={<Loader />}>
              <SupportTrainingResourcesPage />
            </Suspense>
          ),
        },
        {
          path: "support-and-training/helpdesk",
          element: (
            <Suspense fallback={<Loader />}>
              <SupportTrainingHelpDeskPage />
            </Suspense>
          ),
        },
        {
          path: "support-and-training/training",
          element: (
            <Suspense fallback={<Loader />}>
              <SupportTrainingTrainPage />
            </Suspense>
          ),
        },
        {
          path: "support-and-training/knowledge-base",
          element: (
            <Suspense fallback={<Loader />}>
              <SupportTrainingKnowledgeBasePage />
            </Suspense>
          ),
        },
        {
          path: "support-and-training/feedback",
          element: (
            <Suspense fallback={<Loader />}>
              <SupportTrainingFeedbackPage />
            </Suspense>
          ),
        },
        {
          path: "quotation-management",
          element: (
            <Suspense fallback={<Loader />}>
              <QuotationManagementPage />
            </Suspense>
          ),
        },
        {
          path: "notice",
          element: (
            <Suspense fallback={<Loader />}>
              <NoticeManagementPage />
            </Suspense>
          ),
        },
        {
          path: "roles-and-permissions",
          element: (
            <Suspense fallback={<Loader />}>
              <RolesAndPermissionPage />
            </Suspense>
          ),
        },
        {
          path: "content-management",
          element: (
            <Suspense fallback={<Loader />}>
              <ContentManagementPage />
            </Suspense>
          ),
        },
        {
          path: "specification-management",
          element: (
            <Suspense fallback={<Loader />}>
              <SpecificationManagementPage />
            </Suspense>
          ),
        },
      ],
    },
  ]);

  return (
    <>
      <Suspense fallback={<Loader />}>
        <RouterProvider router={router} />
      </Suspense>
    </>
  );
};

export default AppRouter;
